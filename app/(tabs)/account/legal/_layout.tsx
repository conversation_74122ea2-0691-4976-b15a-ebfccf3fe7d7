import { CircleBack } from "@/components/Back";
import { Box } from "@/components/ui/box";
import { Paths } from "@/enum/Paths";
import { Stack } from "expo-router";

export default function LegalLayout() {
  return (
    <Box className="flex-1 flex-col">
      <Box className="py-4">
        <CircleBack to={Paths.ACCOUNT} />
      </Box>
      <Box className="flex-1">
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="msit" />
          <Stack.Screen name="privacy" />
          <Stack.Screen name="terms" />
        </Stack>
      </Box>
    </Box>
  );
}
